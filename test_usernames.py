#!/usr/bin/env python3
"""
Test script for the sequential username generation
"""

import json
import sys
import os

# Add the current directory to the path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_sequential_generation():
    """Test the sequential username generation"""
    
    # Import the functions we need
    from main import load_config, get_next_sequential_username, save_config
    
    print("Testing Sequential Username Generation")
    print("=" * 50)
    
    # Load current config
    config = load_config()
    
    # Show current state
    seq_config = config["sequential_naming"]
    print(f"Current configuration:")
    print(f"  Prefix: {seq_config['prefix']}")
    print(f"  Batch: {seq_config['batch_number']}")
    print(f"  Current Number: {seq_config['current_number']}")
    print(f"  Pattern: {seq_config['pattern']}")
    print()
    
    # Generate 15 test usernames
    print("Generated usernames:")
    for i in range(15):
        username = get_next_sequential_username(config)
        print(f"  {i+1:2d}: {username}")
    
    print()
    print("Test completed!")
    print(f"Next username will be: {seq_config['prefix']}{seq_config['batch_number']:02d}_{seq_config['current_number']:02d}")

if __name__ == "__main__":
    test_sequential_generation()
