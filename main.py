import secrets
import string
import time
import os
import sys
import json
from datetime import date
from selenium import webdriver
from selenium.webdriver.support.ui import Select
import requests
import threading

def status(text):
    os.system('cls' if os.name == 'nt' else 'clear')
    print("\033[1;34m" + text + "\033[0m")

# Configuration management
def load_config():
    """Load configuration from config.json"""
    config_path = "config.json"
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        # Default configuration if file doesn't exist
        default_config = {
            "sequential_naming": {
                "enabled": True,
                "prefix": "account",
                "batch_number": 1,
                "current_number": 7,
                "batch_size": 100,
                "number_format": "{:02d}",
                "pattern": "{prefix}{batch:02d}_{number:02d}"
            },
            "fallback_to_local_names": True,
            "test_mode": False
        }
        save_config(default_config)
        return default_config

def save_config(config):
    """Save configuration to config.json"""
    config_path = "config.json"
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=4)

def get_next_sequential_username(config):
    """Generate the next sequential username and update config"""
    seq_config = config["sequential_naming"]

    # Generate username using the pattern
    username = seq_config["pattern"].format(
        prefix=seq_config["prefix"],
        batch=seq_config["batch_number"],
        number=seq_config["current_number"]
    )

    # Update counters
    seq_config["current_number"] += 1

    # Check if we need to move to next batch
    if seq_config["current_number"] > seq_config["batch_size"]:
        seq_config["batch_number"] += 1
        seq_config["current_number"] = 1

    # Save updated config
    save_config(config)

    return username

def load_local_names():
    """Load names from local files as fallback"""
    try:
        with open('firstnames.txt', 'r') as f:
            first_names = [name.strip() for name in f.readlines() if name.strip()]
        with open('lastnames.txt', 'r') as f:
            last_names = [name.strip() for name in f.readlines() if name.strip()]
        return first_names, last_names
    except FileNotFoundError:
        return [], []

# Load configuration
config = load_config()

#Config
Accounts = 100 #how many accounts
MaxWindows = 3
ActualWindows = 0

# URLs
first_names_url = "https://raw.githubusercontent.com/H20CalibreYT/RobloxAccountCreator/main/firstnames.txt"
last_names_url = "https://raw.githubusercontent.com/H20CalibreYT/RobloxAccountCreator/main/lastnames.txt"
roblox_url = "https://www.roblox.com/"

status("Getting first names...")
first_names_response = requests.get(first_names_url)
status("Getting last names...")
last_names_response = requests.get(last_names_url)

# Check if name loading was successful
if first_names_response.status_code == 200 and last_names_response.status_code == 200:
    first_names = list(set(first_names_response.text.splitlines()))
    last_names = list(set(last_names_response.text.splitlines()))
    # Filter out empty names
    first_names = [name for name in first_names if name.strip()]
    last_names = [name for name in last_names if name.strip()]
else:
    status("Remote name loading failed. Trying local files...")
    first_names, last_names = load_local_names()

# If we still don't have names, use fallback
if not first_names or not last_names:
    if config.get("fallback_to_local_names", True):
        status("Using local name files...")
        first_names, last_names = load_local_names()

    if not first_names or not last_names:
        status("No names available. Please check firstnames.txt and lastnames.txt files.")
        sys.exit()

# File paths
files_path = os.path.dirname(os.path.abspath(sys.argv[0]))
text_files_folder = os.path.join(files_path, "Accounts")
text_file = os.path.join(text_files_folder, f"Accounts_{date.today()}.txt")
text_file2 = os.path.join(text_files_folder, f"AltManagerLogin_{date.today()}.txt")

# Create folder if it does not exist
if not os.path.exists(text_files_folder):
    os.makedirs(text_files_folder)

# Lists of days, months and years
days = [str(i + 1) for i in range(10, 28)]
months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]
years = [str(i + 1) for i in range(1980, 2004)]

# Password generator
def gen_password(length):
    status("Generating a password...")
    chars = string.ascii_letters + string.digits + "Ññ¿?¡!#$%&/()=\\¬|°_-[]*~+"
    password = ''.join(secrets.choice(chars) for _ in range(length))
    return password

#Username generator
def gen_user(first_names, last_names):
    status("Generating a username...")

    # Check if sequential naming is enabled
    if config["sequential_naming"]["enabled"]:
        username = get_next_sequential_username(config)
        status(f"Generated sequential username: {username}")
        return username
    else:
        # Original random generation
        first = secrets.choice(first_names)
        last = secrets.choice(last_names)
        full = f"{first}{last}_{secrets.choice([i for i in range(1, 999)]):03}"
        return full

def create_account(url, first_names, last_names):
    global ActualWindows
    try:
        status("Starting to create an account...")
        cookie_found = False
        username_found = False
        elapsed_time = 0

        status("Initializing webdriver...")
        driver = webdriver.Edge()
        driver.set_window_size(1200, 800)
        driver.set_window_position(0, 0)
        #driver.minimize_window()
        driver.get(url)
        time.sleep(2)

        # HTML items
        status("searching for items on the website")
        username_input = driver.find_element("id", "signup-username")
        username_error = driver.find_element("id", "signup-usernameInputValidation")
        password_input = driver.find_element("id", "signup-password")
        day_dropdown = driver.find_element("id", "DayDropdown")
        month_dropdown = driver.find_element("id", "MonthDropdown")
        year_dropdown = driver.find_element("id", "YearDropdown")
        male_button = driver.find_element("id", "MaleButton")
        female_button = driver.find_element("id", "FemaleButton")
        register_button = driver.find_element("id", "signup-button")

        status("Selecting day...")
        Selection = Select(day_dropdown)
        Selection.select_by_value(secrets.choice(days))
        time.sleep(0.3)

        status("Selecting month...")
        Selection = Select(month_dropdown)
        Selection.select_by_value(secrets.choice(months))
        time.sleep(0.3)

        status("Selecting year...")
        Selection = Select(year_dropdown)
        Selection.select_by_value(secrets.choice(years))
        time.sleep(0.3)

        while not username_found:
            status("Selecting username...")
            username = gen_user(first_names, last_names)
            username_input.clear()
            username_input.send_keys(username)
            time.sleep(1)
            if username_error.text.strip() == "":
                username_found = True

        status("Selecting password...")
        password = gen_password(25)
        password_input.send_keys(password)
        time.sleep(0.3)

        status("Selecting gender...")
        gender = secrets.choice([1,2])
        if gender == 1:
            male_button.click()
        else:
            female_button.click()
        time.sleep(0.5)

        status("Registering account...")
        register_button.click()
        time.sleep(3)

        # Wait until the account creation limit is reset
        try:
            driver.find_element("id", "GeneralErrorText")
            driver.quit()
            for i in range(360):
                status(f"Limit reached, waiting... {i+1}/{360}")
                time.sleep(1)
        except:
            pass

        # Wait until the cookie is found or the maximum time has passed
        while not cookie_found and elapsed_time < 180:
            status("Waiting for the cookie...")
            time.sleep(3)
            elapsed_time += 3
            for cookie in driver.get_cookies():
                if cookie.get('name') == '.ROBLOSECURITY':
                    cookie_found = True
                    break
        if cookie_found:
            status("Cookie found...")
            result = [cookie.get('value'), username, password]
            save_account_info(result)
            save_altmanager_login(result)
            if result is not None:
                status("Successfully created!")
                time.sleep(3)
                ActualWindows -= 1
                status(f"Pestanas abiertas: {ActualWindows}")
                pass

    except:
        status(f"Pestanas abiertas: {ActualWindows}")
        ActualWindows -= 1

# Save account information to text file
def save_account_info(account_info):
    status("Saving account info...")
    with open(text_file, 'a') as file:
        file.write(f"Username: {account_info[1]}\nPassword: {account_info[2]}\nCookie: {account_info[0]}\n\n\n")

# Save login information for AltManager
def save_altmanager_login(account_info):
    with open(text_file2, 'a') as file:
        status("Saving account login (for alt manager)...")
        file.write(f"{account_info[1]}:{account_info[2]}\n")

def test_username_generation(num_tests=10):
    """Test the username generation without creating actual accounts"""
    status("Testing username generation...")
    print("\n" + "="*50)
    print("USERNAME GENERATION TEST")
    print("="*50)

    for i in range(num_tests):
        username = gen_user(first_names, last_names)
        print(f"Test {i+1:2d}: {username}")

    print("="*50)
    print("Test completed. Press Enter to continue or Ctrl+C to exit...")
    input()

# Check if test mode is enabled
if config.get("test_mode", False):
    status("Test mode enabled - showing username generation without creating accounts")
    test_username_generation(20)
else:
    # Display configuration info
    if config["sequential_naming"]["enabled"]:
        seq_config = config["sequential_naming"]
        status(f"Sequential naming enabled: {seq_config['pattern']}")
        status(f"Starting from: {seq_config['prefix']}{seq_config['batch_number']:02d}_{seq_config['current_number']:02d}")
        time.sleep(2)

    # Create accounts
    for _ in range(Accounts):
        while ActualWindows >= MaxWindows:
            status(f"Esperando... {ActualWindows}/{MaxWindows}")
            time.sleep(1)
        ActualWindows += 1
        account_thread = threading.Thread(target=create_account, args=(roblox_url, first_names, last_names))
        account_thread.start()
        time.sleep(1)
