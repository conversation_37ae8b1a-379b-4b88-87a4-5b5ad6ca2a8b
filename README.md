# Roblox Account Creator

This Python script automates the creation of Roblox accounts with unique usernames and secure passwords. The main goal of this project is educational, demonstrating web automation and data manipulation in Python.

**NEW FEATURE**: Sequential username generation with customizable patterns!

### .exe file in releases
(The .exe has the previous version and works perfectly, it's just slower.)

## Requirements

- Python 3.x
- Selenium (install with `pip install selenium`)
- requests (install with `pip install requests`)
- WebDriver compatible with your chosen browser (in this case, Microsoft Edge WebDriver)
- Win11 (maybe)

## Installation

1. Clone or download this repository
2. Install dependencies: `pip install -r requirements.txt`
3. Download the WebDriver corresponding to your browser and place it in the same directory as the script
4. Configure the settings in `config.json` (optional)
5. Run the script: `python main.py`

## Important Note

This project does not include an automatic captcha solving system. Captchas, if encountered during account creation, will need to be solved manually.

## Configuration

### Basic Configuration
In the `main.py` file, you can adjust the number of accounts to create by changing the value of the `Accounts` variable.

### Sequential Username Configuration
The script now supports sequential username generation through the `config.json` file:

```json
{
    "sequential_naming": {
        "enabled": true,
        "prefix": "account",
        "batch_number": 1,
        "current_number": 7,
        "batch_size": 100,
        "pattern": "{prefix}{batch:02d}_{number:02d}"
    },
    "fallback_to_local_names": true,
    "test_mode": false
}
```

#### Configuration Options:
- `enabled`: Enable/disable sequential naming (true/false)
- `prefix`: The prefix for usernames (e.g., "account")
- `batch_number`: Current batch number (starts at 1)
- `current_number`: Current number within the batch (starts at 7 as requested)
- `batch_size`: Maximum numbers per batch before incrementing batch number
- `pattern`: Username pattern using Python string formatting
- `fallback_to_local_names`: Use local name files if remote URLs fail
- `test_mode`: Run in test mode to preview usernames without creating accounts

#### Example Sequential Usernames:
With the default configuration, usernames will be generated as:
- account01_07
- account01_08
- account01_09
- account01_10
- ... and so on

## Test Mode

To test the username generation without creating actual accounts:
1. Set `"test_mode": true` in `config.json`
2. Run `python main.py`
3. The script will show 20 sample usernames and exit

## Files

- `main.py`: Main script
- `config.json`: Configuration file for sequential naming
- `requirements.txt`: Python dependencies
- `firstnames.txt`: Local fallback first names
- `lastnames.txt`: Local fallback last names
- `test_usernames.py`: Standalone test script for username generation

## Recommendations

- Use this script responsibly and ethically.
- Avoid mass account creation, as it may violate Roblox's terms of service.
- Consider privacy and security when sharing or storing any information generated by the script.

## Legal Disclaimer

This project is for educational purposes only. The author assumes no responsibility for misuse of this script.
