# Roblox Account Creator

This Python script automates the creation of Roblox accounts with unique usernames and secure passwords. The main goal of this project is educational, demonstrating web automation and data manipulation in Python.

### .exe file in releases
(The .exe has the previous version and works perfectly, it's just slower.)

## Requirements

- Python 3.x
- Selenium (install with `pip install selenium`)
- secrets (install with `pip install secrets`)
- requests (install with `pip install requests`)
- WebDriver compatible with your chosen browser (in this case, Microsoft Edge WebDriver)
- Win11 (maybe)

## Important Note

This project does not include an automatic captcha solving system. Captchas, if encountered during account creation, will need to be solved manually.

## How to Use

1. Copy the `main.py` script.
2. Install dependencies: `pip install selenium secrets requests`
3. Download the WebDriver corresponding to your browser and place it in the same directory as the script.
4. Run the script: `python main.py`

## Configuration

In the `main.py` file, you can adjust the number of accounts to create by changing the value of the `Accounts` variable.

## Recommendations

- Use this script responsibly and ethically.
- Avoid mass account creation, as it may violate Roblox's terms of service.
- Consider privacy and security when sharing or storing any information generated by the script.

## Legal Disclaimer

This project is for educational purposes only. The author assumes no responsibility for misuse of this script.
